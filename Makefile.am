SUBDIRS = . capture db viewer

if BUILD_CONT3XT
SUBDIRS += cont3xt
endif

if BUILD_WISE
SUBDIRS += wiseService
endif

if BUILD_PARLIAMENT
SUBDIRS += parliament
endif

if BUILD_RELEASE
SUBDIRS += release
endif

SUBDIRS += tests

install-exec-local:
	$(MKDIR_P) $(DESTDIR)@prefix@
	# 检查是否存在离线npm包，优先使用离线安装
	@if [ -d "npm_offline_packages" ] && [ -f "npm_offline_packages/install_npm_offline.sh" ]; then \
		echo "检测到npm离线包，使用离线安装模式..."; \
		npm_offline_packages/install_npm_offline.sh $(DESTDIR)@prefix@ production; \
	elif [ -f "npm_offline_packages/node_modules_production.tar.gz" ]; then \
		echo "检测到npm离线压缩包，直接解压..."; \
		tar -xzf npm_offline_packages/node_modules_production.tar.gz -C $(DESTDIR)@prefix@; \
	else \
		echo "未检测到npm离线包，使用在线安装..."; \
		npm ci; \
		cp -pr node_modules $(DESTDIR)@prefix@/; \
	fi
	cp -pr common $(DESTDIR)@prefix@
	cp -pr assets $(DESTDIR)@prefix@
	@INSTALL@ package.json $(DESTDIR)@prefix@/package.json
	# 只有在线安装时才需要这些步骤
	@if [ ! -d "npm_offline_packages" ]; then \
		@INSTALL@ package-lock.json $(DESTDIR)@prefix@/package-lock.json; \
		(cd $(DESTDIR)@prefix@ ; npm ci --production); \
		rm -f $(DESTDIR)@prefix@/package-lock.json; \
	fi

check-local:
	# 检查是否存在离线npm包，优先使用离线安装
	@if [ -d "npm_offline_packages" ] && [ -f "npm_offline_packages/install_npm_offline.sh" ]; then \
		echo "检测到npm离线包，使用离线安装进行检查..."; \
		npm_offline_packages/install_npm_offline.sh . full; \
	elif [ -f "npm_offline_packages/node_modules_full.tar.gz" ]; then \
		echo "检测到npm离线完整包，直接解压..."; \
		tar -xzf npm_offline_packages/node_modules_full.tar.gz; \
	else \
		echo "未检测到npm离线包，使用在线安装进行检查..."; \
		npm ci; \
	fi

config:
	$(DESTDIR)@prefix@/bin/Configure
