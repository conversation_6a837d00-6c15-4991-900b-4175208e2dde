# Makefile npm离线安装指南

本指南说明如何修改Makefile.in以支持npm离线安装，并与easybutton-build.sh集成。

## 问题解决

### 1. 修改Makefile.in支持离线npm包

我们已经修改了`Makefile.in`文件，使其能够自动检测并使用离线npm包：

#### 修改的部分

**install-exec-local目标**:
```makefile
install-exec-local:
	$(MKDIR_P) $(DESTDIR)@prefix@
	# 检查是否存在离线npm包，优先使用离线安装
	@if [ -d "npm_offline_packages" ] && [ -f "npm_offline_packages/install_npm_offline.sh" ]; then \
		echo "检测到npm离线包，使用离线安装模式..."; \
		npm_offline_packages/install_npm_offline.sh $(DESTDIR)@prefix@ production; \
	elif [ -f "npm_offline_packages/node_modules_production.tar.gz" ]; then \
		echo "检测到npm离线压缩包，直接解压..."; \
		tar -xzf npm_offline_packages/node_modules_production.tar.gz -C $(DESTDIR)@prefix@; \
	else \
		echo "未检测到npm离线包，使用在线安装..."; \
		npm ci; \
		cp -pr node_modules $(DESTDIR)@prefix@/; \
	fi
	cp -pr common $(DESTDIR)@prefix@
	cp -pr assets $(DESTDIR)@prefix@ 
	@INSTALL@ package.json $(DESTDIR)@prefix@/package.json
	# 只有在线安装时才需要这些步骤
	@if [ ! -d "npm_offline_packages" ]; then \
		@INSTALL@ package-lock.json $(DESTDIR)@prefix@/package-lock.json; \
		(cd $(DESTDIR)@prefix@ ; npm ci --production); \
		rm -f $(DESTDIR)@prefix@/package-lock.json; \
	fi
```

**check-local目标**:
```makefile
check-local:
	# 检查是否存在离线npm包，优先使用离线安装
	@if [ -d "npm_offline_packages" ] && [ -f "npm_offline_packages/install_npm_offline.sh" ]; then \
		echo "检测到npm离线包，使用离线安装进行检查..."; \
		npm_offline_packages/install_npm_offline.sh . full; \
	elif [ -f "npm_offline_packages/node_modules_full.tar.gz" ]; then \
		echo "检测到npm离线完整包，直接解压..."; \
		tar -xzf npm_offline_packages/node_modules_full.tar.gz; \
	else \
		echo "未检测到npm离线包，使用在线安装进行检查..."; \
		npm ci; \
	fi
```

### 2. 修复npm下载脚本错误

创建了改进的`download_npm_packages_offline_v2.sh`脚本，解决了以下问题：

#### 修复的问题

1. **npm警告**: 使用`--omit=dev`替代`--production`
2. **网络超时**: 增加重试机制和超时设置
3. **SSL问题**: 设置`strict-ssl=false`
4. **配置问题**: 使用环境变量和临时配置文件

#### 改进的配置

```bash
# npm环境配置
export NPM_CONFIG_CACHE="${CACHE_DIR}"
export NPM_CONFIG_AUDIT=false
export NPM_CONFIG_FUND=false
export NPM_CONFIG_UPDATE_NOTIFIER=false
export NPM_CONFIG_FETCH_RETRY_MINTIMEOUT=20000
export NPM_CONFIG_FETCH_RETRY_MAXTIMEOUT=120000
export NPM_CONFIG_FETCH_RETRIES=3
export NPM_CONFIG_REGISTRY=https://registry.npmjs.org/
export NPM_CONFIG_STRICT_SSL=false
```

## 使用方法

### 方法一：完整流程（推荐）

```bash
# 1. 下载npm离线包
chmod +x download_npm_packages_offline_v2.sh
./download_npm_packages_offline_v2.sh

# 2. 确保npm_offline_packages目录在项目根目录
ls npm_offline_packages/
# 应该看到:
# - install_npm_offline.sh
# - node_modules_production.tar.gz
# - node_modules_full.tar.gz
# - 其他文件...

# 3. 使用easybutton-build.sh进行安装
./easybutton-build.sh --install
# 在第686行会自动调用 make install，此时会使用离线npm包
```

### 方法二：手动测试

```bash
# 1. 测试npm离线包下载
./download_npm_packages_offline_v2.sh

# 2. 测试Makefile集成
chmod +x test_makefile_npm_offline.sh
./test_makefile_npm_offline.sh

# 3. 手动测试make install
./configure --prefix=/opt/arkime
make install  # 会自动使用npm离线包
```

### 方法三：仅下载npm包

```bash
# 如果只需要npm离线包
./download_npm_packages_offline_v2.sh

# 手动安装到指定目录
./npm_offline_packages/install_npm_offline.sh /opt/arkime production
```

## 工作原理

### 自动检测机制

Makefile.in使用以下优先级进行npm安装：

1. **优先级1**: 如果存在`npm_offline_packages/install_npm_offline.sh`
   - 使用离线安装脚本
   - 支持production和full两种模式

2. **优先级2**: 如果存在`npm_offline_packages/node_modules_production.tar.gz`
   - 直接解压预打包的node_modules

3. **优先级3**: 在线安装（回退机制）
   - 使用传统的`npm ci`命令

### 与easybutton-build.sh集成

easybutton-build.sh在第686行执行：
```bash
sudo env "PATH=$TDIR/bin:$PATH" $MAKE install
```

此时会调用Makefile的install目标，进而调用install-exec-local，自动使用npm离线包。

## 验证安装

### 检查npm离线包

```bash
# 检查离线包完整性
ls -la npm_offline_packages/
du -sh npm_offline_packages/

# 测试离线安装脚本
./npm_offline_packages/install_npm_offline.sh /tmp/test_install production
ls /tmp/test_install/node_modules/
```

### 检查Makefile集成

```bash
# 运行测试脚本
./test_makefile_npm_offline.sh

# 手动测试
make check-local  # 应该使用离线包
make install      # 应该使用离线包
```

### 检查最终安装

```bash
# 检查安装结果
ls -la /opt/arkime/node_modules/
node -e "console.log('Node.js modules loaded successfully')" -p /opt/arkime

# 验证关键依赖
ls /opt/arkime/node_modules/ | grep -E "(express|vue|elasticsearch)"
```

## 故障排除

### 常见问题

#### 1. npm下载失败

**错误**: `npm error A complete log of this run can be found in...`

**解决方案**:
```bash
# 清理npm缓存
npm cache clean --force

# 检查网络连接
curl -I https://registry.npmjs.org/

# 使用改进的下载脚本
./download_npm_packages_offline_v2.sh
```

#### 2. Makefile不使用离线包

**症状**: 仍然执行`npm ci`

**解决方案**:
```bash
# 检查npm_offline_packages目录
ls -la npm_offline_packages/

# 确保安装脚本可执行
chmod +x npm_offline_packages/install_npm_offline.sh

# 重新生成Makefile
./configure --prefix=/opt/arkime
```

#### 3. 权限问题

**症状**: 安装时权限错误

**解决方案**:
```bash
# 修复npm离线包权限
chmod -R 755 npm_offline_packages/
chmod +x npm_offline_packages/install_npm_offline.sh

# 修复安装目录权限
sudo chown -R arkime:arkime /opt/arkime/
```

#### 4. Node.js版本不匹配

**症状**: 某些包无法正常工作

**解决方案**:
```bash
# 检查Node.js版本
node --version

# 重新下载适合当前版本的包
rm -rf npm_offline_packages/
./download_npm_packages_offline_v2.sh
```

## 高级配置

### 自定义安装路径

```bash
# 修改Makefile.in中的prefix
./configure --prefix=/custom/path

# 或在make时指定
make install DESTDIR=/custom/root prefix=/custom/path
```

### 自定义npm配置

编辑`npm_offline_packages/.npmrc`:
```ini
cache=./.npm_cache
audit=false
fund=false
registry=https://custom-registry.com/
```

### 多环境支持

```bash
# 为不同环境创建不同的离线包
NODE_ENV=production ./download_npm_packages_offline_v2.sh
mv npm_offline_packages npm_offline_packages_prod

NODE_ENV=development ./download_npm_packages_offline_v2.sh
mv npm_offline_packages npm_offline_packages_dev
```

## 总结

通过以上修改，我们实现了：

1. ✅ **Makefile.in支持离线npm包** - 自动检测并优先使用离线包
2. ✅ **修复npm下载脚本错误** - 解决网络和配置问题
3. ✅ **与easybutton-build.sh集成** - 在第686行自动使用离线包
4. ✅ **完整的测试和验证** - 提供测试脚本确保功能正常

现在您可以在完全离线的环境中使用easybutton-build.sh进行Arkime安装，无需担心npm依赖问题！
