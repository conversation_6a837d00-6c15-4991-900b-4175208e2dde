# Arkime 在 CentOS 7.4 上的部署指南

## 目录
- [概述](#概述)
- [系统要求](#系统要求)
- [部署方式](#部署方式)
- [在线部署](#在线部署)
- [离线部署](#离线部署)
- [配置说明](#配置说明)
- [服务管理](#服务管理)
- [故障排除](#故障排除)
- [性能优化](#性能优化)

## 概述

Arkime是一个大规模、开源的网络分析和数据包捕获系统。本指南详细说明了如何在CentOS 7.4系统上部署Arkime。

**目标服务器信息：**
- 服务器地址：**************
- 操作系统：CentOS 7.4
- 用户名/密码：root/VictoR#.0.0
- 部署目录：/home/<USER>/DataSource

## 系统要求

### 硬件要求
- **CPU**: 最少4核，推荐8核或更多
- **内存**: 最少8GB，推荐16GB或更多
- **存储**: 
  - 系统盘：最少50GB
  - 数据盘：根据数据保留需求，推荐500GB或更多
- **网络**: 千兆网卡，支持数据包捕获

### 软件要求
- CentOS 7.4 或更高版本
- Root权限
- 网络连接（在线安装时需要）

### 端口要求
- **8005**: Arkime Web界面
- **9200-9300**: Elasticsearch
- **22**: SSH管理

## 部署方式

本项目提供两种部署方式：


## 离线部署


### 步骤1: 准备部署文件

```bash
# 连接到目标服务器
ssh root@**************

# 进入部署目录
cd /home/<USER>/DataSource

# 克隆项目（如果已有项目文件，跳过此步）
git clone https://github.com/arkime/arkime.git

# 准备依赖项
cp  curl-8.4.0.tar.gz arkime/thirdparty/
cp  glib-2.72.4.tar.xz arkime/thirdparty/
cp  libmaxminddb-1.7.1.tar.gz arkime/thirdparty/
cp  libpcap-1.10.4.tar.gz arkime/thirdparty/
cp  lua-5.3.6.tar.gz arkime/thirdparty/
cp  nghttp2-1.57.0.tar.gz arkime/thirdparty/
cp  zstd-1.5.5.tar.gz arkime/thirdparty/
cp node-v20.19.2-linux-x64-glibc-217.tar.xz arkime/
rpm -ivh  elasticsearch-oss-7.10.2-x86_64.rpm
yumi -y install --skip-broken wget curl pcre pcre-devel pkgconfig flex bison gcc-c++ zlib-devel e2fsprogs-devel openssl-devel file-devel make gettext libuuid-devel perl-JSON bzip2-libs bzip2-devel perl-libwww-perl libpng-devel xz libffi-devel readline-devel libtool libyaml-devel perl-Socket6 perl-Test-Differences perl-Try-Tiny

```

### 步骤2: 执行部署

```bash
# 运行部署脚本
cd /home/<USER>/DataSource/arkime
./easybutton-build.sh --install
sudo make config
```

部署过程大约需要30-60分钟，具体时间取决于网络速度和服务器性能。

### 步骤3: 验证安装

```bash
# 检查服务状态
/opt/arkime/bin/arkime-status.sh

# 访问Web界面
curl -k https://localhost:8005
```


## 配置说明

### 主配置文件

Arkime的主配置文件位于 `/opt/arkime/etc/config.ini`

#### 关键配置项

```ini
[default]
# Elasticsearch连接
elasticsearch=http://127.0.0.1:9200

# 网络接口（根据实际情况修改）
interface=eth0

# 数据存储目录
pcapDir=/opt/arkime/data/raw
logDir=/opt/arkime/logs

# Web界面端口
viewPort=8005

# 安全配置
passwordSecret=CHANGE_ME_PLEASE_TO_SOMETHING_UNIQUE
httpRealm=Arkime

# 数据保留设置
maxFileSizeG=12
minFreeSpaceG=100
```

#### 网络接口配置

查看可用网络接口：
```bash
ip addr show
```

修改配置文件中的interface参数：
```bash
sed -i 's/interface=eth0/interface=ens33/g' /opt/arkime/etc/config.ini
```

### Elasticsearch配置

配置文件位于 `/etc/elasticsearch/elasticsearch.yml`

```yaml
cluster.name: arkime
node.name: arkime-node-1
network.host: 127.0.0.1
http.port: 9200
discovery.type: single-node
xpack.security.enabled: false
```

## 服务管理

### 启动服务

```bash
# 启动所有Arkime服务
/opt/arkime/bin/arkime-start.sh

# 或者单独启动
systemctl start elasticsearch
systemctl start arkimecapture
systemctl start arkimeviewer
```

### 停止服务

```bash
# 停止所有Arkime服务
/opt/arkime/bin/arkime-stop.sh

# 或者单独停止
systemctl stop arkimeviewer
systemctl stop arkimecapture
systemctl stop elasticsearch
```

### 查看服务状态

```bash
# 查看所有服务状态
/opt/arkime/bin/arkime-status.sh

# 查看日志
journalctl -u arkimecapture -f
journalctl -u arkimeviewer -f
journalctl -u elasticsearch -f
```

### 设置开机自启

```bash
systemctl enable elasticsearch
systemctl enable arkimecapture
systemctl enable arkimeviewer
```

## 用户管理

### 创建管理员用户

```bash
cd /opt/arkime/viewer
node addUser.js admin "Admin User" admin --admin
```

### 创建普通用户

```bash
cd /opt/arkime/viewer
node addUser.js username "User Name" password
```

## 访问Web界面

1. 打开浏览器访问：`https://**************:8005`
2. 使用创建的用户名和密码登录
3. 首次登录后，建议修改默认密码

## 数据捕获测试

### 开始捕获

服务启动后，Arkime会自动开始捕获网络流量。

### 验证捕获

```bash
# 查看捕获统计
curl -s http://localhost:9200/_cat/indices?v | grep arkime

# 查看PCAP文件
ls -la /opt/arkime/data/raw/
```

## 故障排除

### 常见问题

#### 1. Elasticsearch启动失败

**症状**: Elasticsearch服务无法启动
**解决方案**:
```bash
# 检查Java版本
java -version

# 检查内存设置
cat /etc/elasticsearch/jvm.options | grep Xms

# 调整内存设置（如果内存不足）
echo "-Xms1g" >> /etc/elasticsearch/jvm.options
echo "-Xmx1g" >> /etc/elasticsearch/jvm.options
```

#### 2. 网络接口错误

**症状**: Arkime无法捕获数据包
**解决方案**:
```bash
# 查看网络接口
ip addr show

# 修改配置文件
vim /opt/arkime/etc/config.ini
# 修改interface参数为正确的接口名
```

#### 3. 权限问题

**症状**: 服务启动时权限错误
**解决方案**:
```bash
# 修复权限
chown -R arkime:arkime /opt/arkime
chmod -R 755 /opt/arkime
chmod 600 /opt/arkime/etc/arkime.key
```

#### 4. 磁盘空间不足

**症状**: 数据写入失败
**解决方案**:
```bash
# 检查磁盘空间
df -h

# 清理旧数据
find /opt/arkime/data/raw -name "*.pcap" -mtime +7 -delete

# 调整保留策略
vim /opt/arkime/etc/config.ini
# 修改minFreeSpaceG参数
```

### 日志查看

```bash
# Arkime Capture日志
tail -f /opt/arkime/logs/capture.log

# Arkime Viewer日志
journalctl -u arkimeviewer -f

# Elasticsearch日志
tail -f /var/log/elasticsearch/arkime.log
```

### 性能监控

```bash
# 系统资源监控
htop

# 网络流量监控
iotop
nethogs

# Elasticsearch状态
curl -s http://localhost:9200/_cluster/health?pretty
```

## 性能优化

### 系统优化

```bash
# 调整网络缓冲区
echo 'net.core.rmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' >> /etc/sysctl.conf
sysctl -p
```

### Arkime优化

```bash
# 编辑配置文件
vim /opt/arkime/etc/config.ini

# 添加性能优化参数
maxPackets=10000
packetThreads=2
pcapWriteSize=2560000
```

### Elasticsearch优化

```bash
# 编辑JVM设置
vim /etc/elasticsearch/jvm.options

# 设置合适的堆内存（系统内存的50%）
-Xms4g
-Xmx4g
```

## 备份和恢复

### 配置备份

```bash
# 备份配置文件
tar -czf arkime_config_backup_$(date +%Y%m%d).tar.gz \
    /opt/arkime/etc/ \
    /etc/elasticsearch/
```

### 数据备份

```bash
# 备份Elasticsearch数据
curl -X PUT "localhost:9200/_snapshot/arkime_backup" -H 'Content-Type: application/json' -d'
{
  "type": "fs",
  "settings": {
    "location": "/opt/arkime/backup"
  }
}'
```

## 安全建议

1. **修改默认密码**: 安装完成后立即修改所有默认密码
2. **启用HTTPS**: 配置SSL证书，确保Web访问安全
3. **防火墙配置**: 只开放必要的端口
4. **定期更新**: 保持系统和软件包的最新状态
5. **访问控制**: 配置适当的用户权限和访问控制

## 高级配置

### 多网卡配置
```ini
# 监听多个网络接口
interface=eth0;eth1;eth2

# 或使用通配符
interface=eth*
```

### 性能调优配置
```ini
# 增加数据包处理线程
packetThreads=4

# 调整缓冲区大小
maxPackets=20000
pcapWriteSize=2560000

# 启用数据压缩
compressES=true
```

### 集群配置
```ini
# 多节点Elasticsearch
elasticsearch=http://es1:9200;http://es2:9200;http://es3:9200

# 集群名称
cluster=arkime-cluster
```

## 监控和维护

### 系统监控脚本
```bash
#!/bin/bash
# arkime_monitor.sh - Arkime系统监控脚本

# 检查服务状态
check_services() {
    services=("elasticsearch" "arkimecapture" "arkimeviewer")
    for service in "${services[@]}"; do
        if systemctl is-active --quiet "$service"; then
            echo "✓ $service: 运行中"
        else
            echo "✗ $service: 已停止"
        fi
    done
}

# 检查磁盘空间
check_disk_space() {
    local usage=$(df /opt/arkime/data | awk 'NR==2 {print $5}' | sed 's/%//')
    if [[ $usage -gt 80 ]]; then
        echo "⚠ 磁盘使用率: ${usage}% (警告)"
    else
        echo "✓ 磁盘使用率: ${usage}%"
    fi
}

# 检查Elasticsearch健康状态
check_elasticsearch() {
    local health=$(curl -s http://localhost:9200/_cluster/health | jq -r '.status' 2>/dev/null)
    echo "✓ Elasticsearch状态: ${health:-未知}"
}

check_services
check_disk_space
check_elasticsearch
```

### 自动清理脚本
```bash
#!/bin/bash
# arkime_cleanup.sh - 自动清理旧数据

# 清理7天前的PCAP文件
find /opt/arkime/data/raw -name "*.pcap" -mtime +7 -delete

# 清理Elasticsearch旧索引
curl -X DELETE "localhost:9200/arkime_sessions3-$(date -d '30 days ago' +%y%m%d)*"
```

## 安全加固

### SSL/TLS配置
```bash
# 生成自签名证书
openssl req -x509 -newkey rsa:4096 -keyout /opt/arkime/etc/arkime.key \
    -out /opt/arkime/etc/arkime.cert -days 365 -nodes \
    -subj "/C=CN/ST=Beijing/L=Beijing/O=Company/CN=arkime.company.com"

# 配置文件中启用HTTPS
echo "viewPort=8005" >> /opt/arkime/etc/config.ini
echo "certFile=/opt/arkime/etc/arkime.cert" >> /opt/arkime/etc/config.ini
echo "keyFile=/opt/arkime/etc/arkime.key" >> /opt/arkime/etc/config.ini
```

### 防火墙配置
```bash
# 开放必要端口
firewall-cmd --permanent --add-port=8005/tcp  # Arkime Web
firewall-cmd --permanent --add-port=9200/tcp  # Elasticsearch (仅内网)
firewall-cmd --reload
```

### 用户权限管理
```bash
# 创建只读用户
cd /opt/arkime/viewer
node addUser.js readonly "只读用户" password --roles "arkimeUser"

# 创建分析师用户
node addUser.js analyst "分析师" password --roles "arkimeUser,cont3xtUser"
```

## 数据备份策略

### 配置备份
```bash
#!/bin/bash
# backup_config.sh
BACKUP_DIR="/backup/arkime/$(date +%Y%m%d)"
mkdir -p "$BACKUP_DIR"

# 备份配置文件
cp -r /opt/arkime/etc "$BACKUP_DIR/"
cp -r /etc/elasticsearch "$BACKUP_DIR/"

# 备份用户数据
curl -X GET "localhost:9200/arkime_users/_search?pretty" > "$BACKUP_DIR/users.json"
```

### 数据备份
```bash
# Elasticsearch快照备份
curl -X PUT "localhost:9200/_snapshot/arkime_backup" -H 'Content-Type: application/json' -d'
{
  "type": "fs",
  "settings": {
    "location": "/backup/elasticsearch"
  }
}'

# 创建快照
curl -X PUT "localhost:9200/_snapshot/arkime_backup/snapshot_$(date +%Y%m%d)" -H 'Content-Type: application/json' -d'
{
  "indices": "arkime_*",
  "ignore_unavailable": true,
  "include_global_state": false
}'
```

## 联系支持

如果在部署过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查系统日志和Arkime日志
3. 运行诊断脚本：`/opt/arkime/bin/arkime-status.sh`
4. 参考官方文档：https://arkime.com/
5. 社区支持：https://github.com/arkime/arkime/issues

## 附录

### 常用命令速查
```bash
# 服务管理
systemctl start/stop/restart arkimecapture
systemctl start/stop/restart arkimeviewer
systemctl start/stop/restart elasticsearch

# 日志查看
tail -f /opt/arkime/logs/capture.log
journalctl -u arkimeviewer -f
tail -f /var/log/elasticsearch/arkime.log

# 用户管理
cd /opt/arkime/viewer
node addUser.js <username> "<full name>" <password> [--admin]

# 数据库管理
cd /opt/arkime/db
./db.pl http://localhost:9200 info
./db.pl http://localhost:9200 optimize
```

### 配置文件模板
完整的配置文件模板请参考项目中的 `release/config.ini.sample` 文件。

---

**注意**: 本指南基于Arkime最新版本编写，具体版本可能会有差异。建议在生产环境部署前，先在测试环境中验证所有功能。

**版本信息**:
- Arkime: 最新版本
- Node.js: 20.19.2
- Elasticsearch: 7.17.15
- 目标系统: CentOS 7.4
